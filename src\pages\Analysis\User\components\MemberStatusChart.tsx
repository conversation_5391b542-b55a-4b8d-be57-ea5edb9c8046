import { Pie } from '@ant-design/plots';
import { Empty } from 'antd';
import React from 'react';

interface MemberStatusChartProps {
  data: API.CustomerMemberStatusDistribution[];
}

const MemberStatusChart: React.FC<MemberStatusChartProps> = ({ data }) => {
  if (!data || data.length === 0) {
    return <Empty description="暂无数据" />;
  }

  // 转换数据格式
  const chartData = data.map(item => ({
    type: item.statusName,
    value: item.count,
    percentage: item.percentage,
  }));

  const config = {
    data: chartData,
    angleField: 'value',
    colorField: 'type',
    radius: 0.6,
    innerRadius: 0.3,
    height: 300,
    label: {
      type: 'inner',
      offset: '-30%',
      content: ({ value, percentage }: any) => {
        return `${value}人\n${(percentage * 100).toFixed(1)}%`;
      },
      style: {
        fontSize: 12,
        textAlign: 'center' as const,
        fill: '#fff',
        fontWeight: 'bold',
        lineHeight: 1.2,
      },
    },
    legend: {
      position: 'bottom' as const,
      itemName: {
        formatter: (text: string, item: any) => {
          const data = item.data;
          return `${text}: ${data.value}人 (${data.percentage.toFixed(1)}%)`;
        },
      },
    },
    color: ['#52c41a', '#faad14'],
    tooltip: {
      formatter: (datum: any) => {
        return {
          name: datum.type,
          value: `${datum.value}人 (${datum.percentage.toFixed(1)}%)`,
        };
      },
    },
    interactions: [
      {
        type: 'element-active',
      },
    ],
    statistic: {
      title: false,
      content: {
        style: {
          whiteSpace: 'pre-wrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          fontSize: '16px',
          fontWeight: 'bold',
          color: '#262626',
        },
        content: '会员分布',
      },
    },
  };

  return <Pie {...config} />;
};

export default MemberStatusChart;
