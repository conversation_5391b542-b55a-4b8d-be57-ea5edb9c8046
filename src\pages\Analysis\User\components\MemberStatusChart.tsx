import { Pie } from '@ant-design/plots';
import { Empty } from 'antd';
import React from 'react';

interface MemberStatusChartProps {
  data: API.CustomerMemberStatusDistribution[];
}

const MemberStatusChart: React.FC<MemberStatusChartProps> = ({ data }) => {
  if (!data || data.length === 0) {
    return <Empty description="暂无数据" />;
  }

  // 转换数据格式
  const chartData = data.map(item => ({
    type: item.statusName,
    value: item.count,
    percentage: item.percentage,
  }));

  const config = {
    data: chartData,
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    height: 300,
    label: {
      text: (d: any) => `${d.type}\n${d.value}人 (${d?.percentage.toFixed(1)}%)`,
      position: 'spider' as const,
    },
    legend: {
      color: {
        title: false,
        position: 'bottom' as const,
        rowPadding: 5,
      },
    },
    color: ['#52c41a', '#faad14'],
    tooltip: {
      title: (d: any) => d.type,
      items: [
        {
          field: 'value',
          name: '人数',
          valueFormatter: (value: any) => `${value}人`,
        },
      ],
    },
    interactions: [
      {
        type: 'element-active',
      },
    ],
    statistic: {
      title: {
        style: {
          fontSize: '14px',
          color: '#8c8c8c',
        },
        content: '总计',
      },
      content: {
        style: {
          fontSize: '18px',
          fontWeight: 'bold',
          color: '#262626',
        },
        formatter: () => {
          const total = chartData.reduce((sum, item) => sum + item.value, 0);
          return `${total}人`;
        },
      },
    },
  };

  return <Pie {...config} />;
};

export default MemberStatusChart;
