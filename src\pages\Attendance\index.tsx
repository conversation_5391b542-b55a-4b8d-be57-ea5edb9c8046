import { PageContainer } from '@ant-design/pro-components';
import { Card, Tabs } from 'antd';
import React, { useState } from 'react';
import {
  CheckinDetailModal,
  CheckinList,
} from './components';

const EmployeeCheckinManagement: React.FC = () => {
  const [detailVisible, setDetailVisible] = useState(false);
  const [currentCheckin, setCurrentCheckin] = useState<API.EmployeeCheckin>();

  /** 查看打卡详情 */
  const handleViewDetail = (checkin: API.EmployeeCheckin) => {
    setCurrentCheckin(checkin);
    setDetailVisible(true);
  };

  const tabItems = [
    {
      key: 'list',
      label: '打卡记录',
      children: <CheckinList onViewDetail={handleViewDetail} />,
    },
    // {
    //   key: 'statistics',
    //   label: '统计分析',
    //   children: <CheckinStatistics />,
    // },
  ];

  return (
    <PageContainer
      title="员工出车拍照管理"
      content="管理员工出车时的拍照打卡记录，支持查看打卡详情、统计分析和记录管理"
    >
      <Card>
        <Tabs
          defaultActiveKey="list"
          items={tabItems}
          size="large"
          tabBarStyle={{ marginBottom: 24 }}
        />
      </Card>

      {/* 打卡详情模态框 */}
      <CheckinDetailModal
        visible={detailVisible}
        checkin={currentCheckin}
        onClose={() => {
          setDetailVisible(false);
          setCurrentCheckin(undefined);
        }}
      />
    </PageContainer>
  );
};

export default EmployeeCheckinManagement;
