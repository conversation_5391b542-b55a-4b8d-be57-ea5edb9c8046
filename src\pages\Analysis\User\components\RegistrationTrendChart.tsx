import { Area } from '@ant-design/plots';
import { Empty } from 'antd';
import React from 'react';

interface RegistrationTrendChartProps {
  data: API.CustomerRegistrationTrend[];
}

const RegistrationTrendChart: React.FC<RegistrationTrendChartProps> = ({ data }) => {
  if (!data || data.length === 0) {
    return <Empty description="暂无数据" />;
  }

  // 转换数据格式
  const chartData = data.map(item => ({
    date: item.period,
    count: item.count,
  }));

  const config = {
    data: chartData,
    xField: (d: any) => new Date(d.date),
    yField: 'count',
    height: 350,
    smooth: true,
    style: {
      fill: 'linear-gradient(-90deg, white 0%, #975FE4 100%)',
      fillOpacity: 0.6,
    },
    axis: {
      x: {
        title: false,
        size: 40,
        labelFormatter: (d: any) => {
          const date = new Date(d);
          return `${date.getMonth() + 1}/${date.getDate()}`;
        },
      },
      y: {
        title: false,
        size: 36,
        labelFormatter: (d: any) => `${d}人`,
      },
    },
    slider: {
      x: {
        labelFormatter: (d: any) => {
          const date = new Date(d);
          return `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`;
        },
      },
      y: { labelFormatter: '~s' },
    },
    tooltip: {
      title: (d: any) => {
        const date = new Date(d.date);
        return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
      },
      items: [
        {
          field: 'count',
          name: '注册人数',
          valueFormatter: (d: any) => `${d}人`,
        },
      ],
    },
  };

  return <Area {...config} />;
};

export default RegistrationTrendChart;
