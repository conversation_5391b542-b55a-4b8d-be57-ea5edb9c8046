import { Area } from '@ant-design/plots';
import { Empty } from 'antd';
import React from 'react';

interface RegistrationTrendChartProps {
  data: API.CustomerRegistrationTrend[];
}

const RegistrationTrendChart: React.FC<RegistrationTrendChartProps> = ({ data }) => {
  if (!data || data.length === 0) {
    return <Empty description="暂无数据" />;
  }

  // 转换数据格式
  const chartData = data.map(item => ({
    date: item.period,
    count: Number(item.count), // 确保count是数字类型
  }));

  // 调试信息
  console.log('原始数据:', data);
  console.log('转换后数据:', chartData);

  const config = {
    data: chartData,
    xField: 'date',
    yField: 'count',
    height: 350,
    smooth: true,
    color: '#1890ff',
    areaStyle: {
      fill: 'linear-gradient(-90deg, white 0%, #1890ff 100%)',
      fillOpacity: 0.6,
    },
    xAxis: {
      type: 'time',
      label: {
        formatter: (text: string) => {
          const date = new Date(text);
          return `${date.getMonth() + 1}/${date.getDate()}`;
        },
      },
    },
    yAxis: {
      nice: true,
      min: 0,
      label: {
        formatter: (text: string) => `${text}人`,
      },
    },
    slider: {
      start: 0.1,
      end: 0.9,
    },
    tooltip: {
      formatter: (datum: any) => {
        const date = new Date(datum.date);
        const dateStr = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
        return {
          name: '注册人数',
          value: `${datum.count}人`,
          title: dateStr,
        };
      },
    },
  };

  return <Area {...config} />;
};

export default RegistrationTrendChart;
