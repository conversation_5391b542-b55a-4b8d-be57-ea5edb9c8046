import { Line } from '@ant-design/plots';
import { Empty } from 'antd';
import React from 'react';

interface RegistrationTrendChartProps {
  data: API.CustomerRegistrationTrend[];
}

const RegistrationTrendChart: React.FC<RegistrationTrendChartProps> = ({ data }) => {
  if (!data || data.length === 0) {
    return <Empty description="暂无数据" />;
  }

  // 转换数据格式
  const chartData = data.map(item => ({
    date: item.period,
    count: item.count,
  }));

  const config = {
    data: chartData,
    xField: 'date',
    yField: 'count',
    smooth: true,
    color: '#1890ff',
    height: 350,
    point: {
      size: 4,
      shape: 'circle',
      style: {
        fill: '#1890ff',
        stroke: '#fff',
        lineWidth: 2,
      },
    },
    lineStyle: {
      lineWidth: 2,
    },
    xAxis: {
      type: 'time',
      tickCount: 5,
      label: {
        formatter: (text: string) => {
          const date = new Date(text);
          return `${date.getMonth() + 1}/${date.getDate()}`;
        },
      },
    },
    yAxis: {
      label: {
        formatter: (text: string) => `${text}人`,
      },
    },
    tooltip: {
      formatter: (datum: any) => {
        const date = new Date(datum.date);
        const dateStr = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
        return {
          name: '注册人数',
          value: `${datum.count}人`,
          title: dateStr,
        };
      },
    },
    area: {
      style: {
        fill: 'l(270) 0:#ffffff 0.5:#7ec2f3 1:#1890ff',
        fillOpacity: 0.3,
      },
    },
    slider: {
      start: 0.1,
      end: 0.9,
    },
  };

  return <Line {...config} />;
};

export default RegistrationTrendChart;
