import { PageContainer } from '@ant-design/pro-components';
import { Card, Col, DatePicker, Row, Statistic, message } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import * as customerStatistics from '@/services/customer-statistics';
import GenderChart from './components/GenderChart';
import MemberStatusChart from './components/MemberStatusChart';
import RegistrationTrendChart from './components/RegistrationTrendChart';

const { RangePicker } = DatePicker;

const UserStatistics: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [overviewData, setOverviewData] = useState<API.CustomerOverviewStats>();
  const [trendData, setTrendData] = useState<API.CustomerRegistrationTrend[]>([]);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(30, 'day'),
    dayjs(),
  ]);

  // 获取概览数据（不传时间参数）
  const fetchOverviewData = async () => {
    try {
      const { errCode, msg, data } = await customerStatistics.overview();

      if (errCode) {
        message.error(msg || '获取概览数据失败');
        return;
      }

      if (data) {
        setOverviewData(data);
      }
    } catch (error) {
      console.error('获取概览数据失败:', error);
      message.error('获取概览数据失败');
    }
  };

  // 获取注册趋势数据
  const fetchTrendData = async (startDate: string, endDate: string) => {
    try {
      const { errCode, msg, data } = await customerStatistics.registrationTrend({
        startDate,
        endDate,
        periodType: 'day',
      });

      if (errCode) {
        message.error(msg || '获取趋势数据失败');
        return;
      }

      if (data) {
        setTrendData(data);
      }
    } catch (error) {
      console.error('获取趋势数据失败:', error);
      message.error('获取趋势数据失败');
    }
  };

  // 初始化数据
  useEffect(() => {
    setLoading(true);
    // 概览数据只在组件初始化时获取一次
    fetchOverviewData().finally(() => {
      setLoading(false);
    });
  }, []);

  // 趋势数据根据日期范围变化
  useEffect(() => {
    const startDate = dateRange[0].format('YYYY-MM-DD');
    const endDate = dateRange[1].format('YYYY-MM-DD');
    fetchTrendData(startDate, endDate);
  }, [dateRange]);

  // 处理日期范围变化
  const handleDateRangeChange = (dates: any) => {
    if (dates && dates.length === 2) {
      setDateRange([dates[0], dates[1]]);
    }
  };

  return (
    <PageContainer
      title="用户数据统计分析"
    >
      {/* 概览统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="总用户数"
              value={overviewData?.totalUsers || 0}
              loading={loading}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="今日新增"
              value={overviewData?.todayNewUsers || 0}
              loading={loading}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="本月新增"
              value={overviewData?.monthNewUsers || 0}
              loading={loading}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 分布图表区域 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        {/* 性别分布图 */}
        <Col xs={24} lg={12}>
          <Card title="性别分布" loading={loading}>
            <GenderChart data={overviewData?.genderDistribution || []} />
          </Card>
        </Col>

        {/* 会员状态分布图 */}
        <Col xs={24} lg={12}>
          <Card title="会员状态分布" loading={loading}>
            <MemberStatusChart data={overviewData?.memberStatusDistribution || []} />
          </Card>
        </Col>
      </Row>

      {/* 注册趋势图 */}
      <Row gutter={[16, 16]}>
        <Col xs={24}>
          <Card
            title="用户注册趋势"
            loading={loading}
            extra={
              <RangePicker
                value={dateRange}
                onChange={handleDateRangeChange}
                allowClear={false}
                format="YYYY-MM-DD"
                size="small"
              />
            }
          >
            <RegistrationTrendChart data={trendData} />
          </Card>
        </Col>
      </Row>
    </PageContainer>
  );
};

export default UserStatistics;
